/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #06b6d4;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background: #ffffff;
    --background-light: #f9fafb;
    --background-dark: #f3f4f6;
    --border-color: #e5e7eb;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    --spacing-4xl: 6rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition-fast);
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: 120px 0 var(--spacing-4xl);
    background: linear-gradient(135deg, var(--background-light) 0%, var(--background) 100%);
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.hero-content h1 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.hero-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-3xl);
}

.btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-large);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-full {
    width: 100%;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-2xl);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    text-align: center;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-placeholder {
    width: 400px;
    height: 400px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-large);
}

.hero-image-placeholder i {
    font-size: 120px;
    color: white;
    opacity: 0.9;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-4xl);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Services Section */
.services {
    padding: var(--spacing-4xl) 0;
    background-color: var(--background-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.service-card {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.service-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* About Section */
.about {
    padding: var(--spacing-4xl) 0;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
    align-items: center;
}

.about-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.feature i {
    color: var(--accent-color);
    font-size: var(--font-size-lg);
}

.feature span {
    font-weight: 500;
    color: var(--text-primary);
}

.about-image-placeholder {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-large);
}

.about-image-placeholder i {
    font-size: 120px;
    color: white;
    opacity: 0.9;
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-4xl) 0;
    background-color: var(--background-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.testimonial-card {
    background: white;
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.testimonial-rating {
    display: flex;
    gap: 4px;
    margin-bottom: var(--spacing-lg);
}

.testimonial-rating i {
    color: #fbbf24;
    font-size: var(--font-size-sm);
}

.testimonial-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
    font-style: italic;
}

.testimonial-author strong {
    color: var(--text-primary);
    font-weight: 600;
}

.testimonial-author span {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    display: block;
}

/* Contact Section */
.contact {
    padding: var(--spacing-4xl) 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4xl);
}

.contact-info h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.contact-info p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.7;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
}

.contact-item i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-top: 4px;
}

.contact-item strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 4px;
}

.contact-item span {
    color: var(--text-secondary);
    line-height: 1.5;
}

.contact-form {
    background: var(--background-light);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Footer */
.footer {
    background-color: var(--text-primary);
    color: white;
    padding: var(--spacing-4xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
}

.footer-logo i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.footer-section p {
    color: #d1d5db;
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: #d1d5db;
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-section ul li i {
    margin-right: var(--spacing-sm);
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-links a:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-xl);
    text-align: center;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-content h1 {
        font-size: var(--font-size-4xl);
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .services-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        padding: 100px 0 var(--spacing-2xl);
    }
    
    .services,
    .about,
    .testimonials,
    .contact {
        padding: var(--spacing-2xl) 0;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: var(--font-size-3xl);
    }
    
    .section-header h2 {
        font-size: var(--font-size-3xl);
    }
    
    .about-text h2,
    .contact-info h2 {
        font-size: var(--font-size-3xl);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
}
