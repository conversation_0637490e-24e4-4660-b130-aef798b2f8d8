/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

:root {
    /* Colors - More standard and professional */
    --primary-color: #0066cc;
    --primary-dark: #0052a3;
    --primary-light: #e6f2ff;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-light: #adb5bd;
    --background: #ffffff;
    --background-light: #f8f9fa;
    --background-section: #ffffff;
    --border-color: #dee2e6;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

    /* Typography - Standard web typography */
    --font-family: 'Inter', system-ui, -apple-system, 'Segoe <PERSON>I', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.5rem;
    --font-size-5xl: 3.5rem;

    /* Spacing - Standard 8px grid system */
    --spacing-1: 0.25rem;   /* 4px */
    --spacing-2: 0.5rem;    /* 8px */
    --spacing-3: 0.75rem;   /* 12px */
    --spacing-4: 1rem;      /* 16px */
    --spacing-5: 1.25rem;   /* 20px */
    --spacing-6: 1.5rem;    /* 24px */
    --spacing-8: 2rem;      /* 32px */
    --spacing-10: 2.5rem;   /* 40px */
    --spacing-12: 3rem;     /* 48px */
    --spacing-16: 4rem;     /* 64px */
    --spacing-20: 5rem;     /* 80px */
    --spacing-24: 6rem;     /* 96px */

    /* Border Radius - Standard values */
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions - Smooth and standard */
    --transition: all 0.2s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container - Standard responsive container */
.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 576px) {
    .container { max-width: 540px; }
}

@media (min-width: 768px) {
    .container { max-width: 720px; }
}

@media (min-width: 992px) {
    .container { max-width: 960px; }
}

@media (min-width: 1200px) {
    .container { max-width: 1140px; }
}

/* Navigation - Clean and standard */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-logo i {
    font-size: var(--font-size-2xl);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
    margin: 0;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: var(--font-size-base);
    padding: var(--spacing-2) 0;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    gap: var(--spacing-1);
    padding: var(--spacing-2);
}

.nav-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--text-primary);
    transition: var(--transition);
    border-radius: 1px;
}

/* Hero Section - Clean and spacious */
.hero {
    padding: calc(64px + var(--spacing-20)) 0 var(--spacing-20);
    background: var(--background-light);
}

.hero-container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-content h1 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-6);
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.hero-content p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
    max-width: 90%;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-12);
}

/* Buttons - Standard and clean */
.btn {
    padding: var(--spacing-3) var(--spacing-6);
    border: none;
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-full {
    width: 100%;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-8);
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-1);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-img {
    width: 100%;
    max-width: 480px;
    height: 360px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-slow);
}

.hero-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Fallback for hero image placeholder (if image fails to load) */
.hero-image-placeholder {
    width: 100%;
    max-width: 480px;
    height: 360px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.hero-image-placeholder i {
    font-size: 80px;
    color: white;
    opacity: 0.9;
}

/* Section Headers - Clean and consistent */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-header h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Services Section - Clean grid layout */
.services {
    padding: var(--spacing-20) 0;
    background-color: var(--background);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-8);
}

.service-card {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-slow);
    text-align: center;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.service-icon {
    width: 64px;
    height: 64px;
    background: var(--primary-light);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-6);
}

.service-icon i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.service-card h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: var(--font-size-base);
}

/* About Section - Clean and professional */
.about {
    padding: var(--spacing-20) 0;
    background-color: var(--background-light);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.about-text h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-6);
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.about-text p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-2) 0;
}

.feature i {
    color: var(--accent-color);
    font-size: var(--font-size-lg);
    width: 20px;
    flex-shrink: 0;
}

.feature span {
    font-weight: 500;
    color: var(--text-primary);
    font-size: var(--font-size-base);
}

.about-img {
    width: 100%;
    height: 360px;
    object-fit: cover;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-slow);
}

.about-img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

/* Fallback for about image placeholder (if image fails to load) */
.about-image-placeholder {
    width: 100%;
    height: 360px;
    background: linear-gradient(135deg, var(--primary-light), var(--background-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--border-color);
}

.about-image-placeholder i {
    font-size: 80px;
    color: var(--primary-color);
    opacity: 0.7;
}

/* Testimonials Section - Clean and trustworthy */
.testimonials {
    padding: var(--spacing-20) 0;
    background-color: var(--background);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-8);
}

.testimonial-card {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-slow);
    border: 1px solid var(--border-color);
}

.testimonial-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.testimonial-rating {
    display: flex;
    gap: var(--spacing-1);
    margin-bottom: var(--spacing-5);
}

.testimonial-rating i {
    color: #fbbf24;
    font-size: var(--font-size-base);
}

.testimonial-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
    font-size: var(--font-size-base);
    font-style: italic;
}

.testimonial-author strong {
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--font-size-base);
}

.testimonial-author span {
    color: var(--text-light);
    font-size: var(--font-size-sm);
    display: block;
    margin-top: var(--spacing-1);
}

/* Contact Section - Clean form design */
.contact {
    padding: var(--spacing-20) 0;
    background-color: var(--background-light);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
}

.contact-info h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-6);
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.contact-info p {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-4);
}

.contact-item i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-1);
    width: 20px;
    flex-shrink: 0;
}

.contact-item strong {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
    font-size: var(--font-size-base);
}

.contact-item span {
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: var(--font-size-base);
}

.contact-form {
    background: white;
    padding: var(--spacing-8);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.form-group {
    margin-bottom: var(--spacing-5);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: var(--transition);
    background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer - Clean and standard */
.footer {
    background-color: #f8f9fa;
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-12);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--primary-color);
}

.footer-logo i {
    font-size: var(--font-size-2xl);
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-6);
    line-height: 1.6;
    font-size: var(--font-size-base);
    max-width: 280px;
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
    color: var(--text-primary);
}

.footer-section ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: var(--spacing-2);
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    font-size: var(--font-size-base);
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-1) 0;
    line-height: 1.5;
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-section ul li i {
    margin-right: var(--spacing-3);
    color: var(--primary-color);
    width: 18px;
    font-size: var(--font-size-base);
    flex-shrink: 0;
    margin-top: 2px;
}

.footer-section ul li span {
    flex: 1;
    line-height: 1.5;
}

/* Special styling for contact links */
.footer-section ul li a[href^="tel"],
.footer-section ul li a[href^="mailto"],
.footer-section ul li a[href*="maps"] {
    color: var(--text-secondary);
}

.footer-section ul li a[href^="tel"]:hover,
.footer-section ul li a[href^="mailto"]:hover,
.footer-section ul li a[href*="maps"]:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-3);
    margin-top: var(--spacing-2);
}

.social-links a {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: var(--transition);
    font-size: var(--font-size-lg);
}

.social-links a:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-6);
    text-align: center;
    color: var(--text-light);
    font-size: var(--font-size-sm);
}

/* Responsive Design - Clean breakpoints */
@media (max-width: 991px) {
    .hero-container,
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
    }

    .hero-content {
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-8);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-content h1 {
        font-size: var(--font-size-4xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-3);
    }

    .btn {
        width: 100%;
        max-width: 280px;
    }

    .services-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .hero {
        padding: calc(64px + var(--spacing-12)) 0 var(--spacing-12);
    }

    .services,
    .about,
    .testimonials,
    .contact {
        padding: var(--spacing-12) 0;
    }

    .section-header {
        margin-bottom: var(--spacing-12);
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-8);
        text-align: center;
    }

    .footer-section p {
        max-width: none;
    }
}

@media (max-width: 576px) {
    .container {
        padding: 0 var(--spacing-4);
    }

    .hero-content h1 {
        font-size: var(--font-size-3xl);
    }

    .section-header h2,
    .about-text h2,
    .contact-info h2 {
        font-size: var(--font-size-3xl);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-6);
        align-items: center;
    }

    .stat {
        align-items: center;
        text-align: center;
    }

    .service-card,
    .testimonial-card,
    .contact-form {
        padding: var(--spacing-6);
    }

    .nav-container {
        padding: 0 var(--spacing-4);
    }
}
