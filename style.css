/* Basic Reset & Global Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    /* Color Palette for Calmness and Clarity */
    --primary-blue: #3498db; /* A more vibrant blue */
    --secondary-green: #2ecc71; /* A brighter green */
    --accent-color: #f39c12; /* A warm orange/gold */
    --light-blue: #ecf0f1; /* A softer light blue */
    --light-gray: #fafafa; /* A slightly warmer light gray */
    --dark-text: #444444; /* Slightly lighter dark text */
    --light-text: #ffffff; /* White text for dark backgrounds */
    --border-color: #cccccc; /* Softer borders */

    /* Typography [1, 2, 3, 4, 5] */
    --font-body: 'Inter', sans-serif;
    --font-heading: 'Playfair Display', serif;
    --font-size-base: 1rem; /* 16px */
    --line-height-base: 1.6;
}

html {
    scroll-behavior: smooth; /* Smooth scrolling for anchor links [6] */
}

body {
    font-family: var(--font-body);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--dark-text);
    background-color: var(--light-gray);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    color: var(--primary-blue);
    margin-bottom: 0.8em;
    line-height: 1.2;
}

h1 { font-size: 3.2rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }

/* Section Spacing */
section {
    padding: 80px 0;
    position: relative;
    overflow: hidden; /* Ensures no overflow from animations */
}

.section-title {
    text-align: center;
    margin-bottom: 20px;
    color: var(--primary-blue);
}

.section-description {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px auto;
    color: var(--dark-text);
}

/* Buttons */
.button {
    display: inline-block;
    padding: 14px 28px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.2s ease; /* Smooth transitions [7, 8] */
    border: 2px solid transparent;
    cursor: pointer;
}

.primary-button {
    background-color: var(--primary-blue);
    color: var(--light-text);
    border-color: var(--primary-blue);
}

.primary-button:hover,.primary-button:focus {
    background-color: darken(var(--primary-blue), 10%);
    transform: translateY(-2px);
}

.secondary-button {
    background-color: transparent;
    color: var(--primary-blue);
    border-color: var(--primary-blue);
}

.secondary-button:hover,.secondary-button:focus {
    background-color: var(--primary-blue);
    color: var(--light-text);
    transform: translateY(-2px);
}

/* Header */
.site-header {
    background-color: var(--light-text);
    padding: 15px 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* More pronounced shadow */
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    max-height: 50px;
    width: auto;
}

.main-nav ul {
    list-style: none;
    display: flex;
    gap: 30px;
}

.main-nav a {
    color: var(--dark-text);
    text-decoration: none;
    font-weight: 600;
    padding: 5px 0;
    position: relative;
}

.main-nav a::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-blue);
    transition: width 0.3s ease;
}

.main-nav a:hover::after,
.main-nav a:focus::after {
    width: 100%;
}

.header-cta {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.hamburger-menu {
    display: none; /* Hidden on desktop */
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    z-index: 1001;
}

.hamburger-menu span {
    display: block;
    width: 25px;
    height: 3px;
    background-color: var(--dark-text);
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

/* Hero Section */
.hero-section {
    background-color: var(--light-blue);
    padding: 100px 0;
    text-align: center;
}

.hero-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 40px;
}

.hero-text {
    flex: 1;
    text-align: left;
    max-width: 600px;
}

.hero-headline {
    color: var(--primary-blue);
    margin-bottom: 20px;
}

.hero-subheadline {
    font-size: 1.25rem;
    color: var(--dark-text);
    margin-bottom: 30px;
}

.hero-ctas {
    display: flex;
    gap: 20px;
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Services Section */
.services-section {
    background-color: var(--light-gray);
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Responsive Grid [9, 10] */
    gap: 30px;
}

.service-card {
    background-color: var(--light-text);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 25px rgba(0, 0, 0, 0.15);
}

.service-card img {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    border-radius: 50%;
    background-color: var(--light-blue);
    padding: 10px;
}

.service-card h3 {
    color: var(--primary-blue);
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.service-card p {
    color: var(--dark-text);
    margin-bottom: 15px;
}

.service-card.learn-more {
    color: var(--secondary-green);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.service-card.learn-more:hover {
    color: darken(var(--secondary-green), 10%);
}

/* About Section */
.about-section {
    background-color: var(--light-text);
}

.about-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 40px;
}

.about-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-text {
    flex: 1;
    text-align: left;
    max-width: 600px;
}

.about-text p {
    margin-bottom: 15px;
}

/* Testimonials Section */
.testimonials-section {
    background-color: var(--light-blue);
    text-align: center;
}

.carousel {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
}

.carousel-content {
    display: flex;
    transition: transform 0.5s ease-in-out; /* Smooth carousel transitions */
}

.testimonial-card {
    min-width: 100%;
    padding: 40px;
    background-color: var(--light-text);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    text-align: center;
    flex-shrink: 0; /* Prevents cards from shrinking */
}

.testimonial-card.quote {
    font-size: 1.2rem;
    font-style: italic;
    margin-bottom: 20px;
    color: var(--dark-text);
}

.testimonial-card.author {
    font-weight: 600;
    color: var(--primary-blue);
    margin-bottom: 10px;
}

.testimonial-card.rating {
    color: var(--accent-orange);
    font-size: 1.5rem;
}

.carousel-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--light-text);
    border: none;
    padding: 10px 15px;
    cursor: pointer;
    font-size: 1.5rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    z-index: 10;
}

.carousel-arrow:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.carousel-arrow.left {
    left: -50px;
}

.carousel-arrow.right {
    right: -50px;
}

.carousel-dots {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.dot.active {
    background-color: var(--primary-blue);
}

/* FAQ Section (Accordion) */
.faq-section {
    background-color: var(--light-gray);
}

.accordion {
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--border-color);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background-color: var(--light-text);
    color: var(--primary-blue);
    padding: 20px;
    width: 100%;
    text-align: left;
    border: none;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.3s ease;
}

.accordion-header:hover,.accordion-header:focus {
    background-color: #f0f0f0;
}

.accordion-header::after {
    content: '+';
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.accordion-header[aria-expanded="true"]::after {
    content: '-';
    transform: rotate(180deg);
}

.accordion-panel {
    background-color: var(--light-text);
    padding: 0 20px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
}

.accordion-panel p {
    padding-bottom: 20px;
    color: var(--dark-text);
}

.accordion-panel.active {
    max-height: 200px; /* Adjust as needed, or use JS to calculate */
    padding: 20px;
}

/* Contact Section & Form */
.contact-section {
    background-color: var(--light-text);
}

.contact-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    justify-content: center;
}

.contact-info,.contact-form-container {
    flex: 1;
    min-width: 300px;
    max-width: 550px;
}

.contact-info h2,.contact-form-container h2 {
    text-align: left;
    margin-bottom: 20px;
}

.contact-info p,.contact-info address {
    margin-bottom: 15px;
    color: var(--dark-text);
}

.contact-info ul {
    list-style: none;
    margin-bottom: 20px;
}

.contact-info ul li {
    margin-bottom: 8px;
    color: var(--dark-text);
}

.contact-info a {
    color: var(--primary-blue);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.social-links a:hover img {
    transform: scale(1.1);
}

.appointment-form {
    background-color: var(--light-gray);
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-text);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group input[type="time"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: var(--font-body);
    font-size: 1rem;
    color: var(--dark-text);
    background-color: var(--light-text);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(42, 98, 143, 0.2);
    outline: none;
}

.form-group small {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
    display: block;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin-top: 4px;
}

.checkbox-group label {
    display: inline;
    font-weight: 400;
}

.checkbox-group label a {
    color: var(--primary-blue);
    text-decoration: none;
}

.checkbox-group label a:hover {
    text-decoration: underline;
}

.appointment-form.primary-button {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
}

/* Footer */
.site-footer {
    background-color: var(--primary-blue);
    color: var(--light-text);
    padding: 60px 0;
    font-size: 0.9rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 30px;
}

.footer-column h3 {
    color: var(--light-text);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.footer-column p {
    margin-bottom: 10px;
}

.footer-column ul {
    list-style: none;
}

.footer-column ul li {
    margin-bottom: 8px;
}

.footer-column a {
    color: var(--light-text);
    text-decoration: none;
    transition: text-decoration 0.3s ease;
}

.footer-column a:hover {
    text-decoration: underline;
}

/* Mobile Responsiveness */
@media (max-width: 992px) {
    .main-nav ul {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: var(--light-text);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 20px 0;
        z-index: 100;
        text-align: center; /* Center align menu items */
    }

    .main-nav ul.active {
        display: flex;
    }

    .main-nav ul li {
        margin: 10px 0;
    }

    .main-nav ul a {
        padding: 10px 20px;
        display: block;
    }

   
       .hamburger-menu {
           display: block;
           position: absolute;
           top: 15px;
           right: 20px;
       }
   
       .header-cta {
           display: none;
       }
   .hero-content,.about-content {
        flex-direction: column;
        text-align: center;
    }

   .hero-text,.about-text {
        text-align: center;
        max-width: 100%;
    }

   .hero-ctas {
        justify-content: center;
    }

   .hero-headline {
        font-size: 2.5rem;
    }

   .hero-subheadline {
        font-size: 1.1rem;
    }

   .service-grid {
        grid-template-columns: 1fr; /* Stack services on small screens */
    }

   .carousel-arrow.left {
        left: 10px;
    }

   .carousel-arrow.right {
        right: 10px;
    }

   .contact-content {
        flex-direction: column;
    }

   .contact-info,.contact-form-container {
        max-width: 100%;
    }

   .contact-info h2,.contact-form-container h2 {
        text-align: center;
    }
}

@media (max-width: 768px) {
    h1 { font-size: 2.8rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.6rem; }

    section {
        padding: 60px 0;
    }

   .button {
        padding: 12px 24px;
        font-size: 0.9rem;
    }

   .hero-headline {
        font-size: 2.2rem;
    }

   .hero-subheadline {
        font-size: 1rem;
    }

   .testimonial-card {
        padding: 30px;
    }

   .testimonial-card.quote {
        font-size: 1.1rem;
    }

   .accordion-header {
        font-size: 1rem;
        padding: 15px;
    }

   .accordion-panel.active {
        padding: 15px;
    }

   .appointment-form {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.8rem; }
    h3 { font-size: 1.4rem; }

   .hero-ctas {
        flex-direction: column;
        gap: 15px;
    }

   .button {
        width: 100%;
    }

   .carousel-arrow {
        padding: 8px 12px;
        font-size: 1.2rem;
    }
}