<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BJMP Bislig - Digital Literacy Needs Infographic</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .chart-container {
            position: relative;
            width: 100%;
            margin-left: auto;
            margin-right: auto;
            max-height: 400px;
            height: 320px;
        }
        @media (min-width: 640px) {
            .chart-container {
                height: 350px;
            }
        }
        @media (min-width: 1024px) {
            .chart-container {
                max-height: 450px;
                height: 400px;
            }
        }
        .flow-arrow {
            color: #d1d5db;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800">

    <div class="container mx-auto p-4 md:p-8">
        
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-extrabold text-[#003f5c] mb-2">Empowering BJMP Bislig City</h1>
            <p class="text-lg md:text-xl text-gray-600">A Data-Driven Roadmap for Advanced Digital Literacy</p>
        </header>

        <main class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

            <div class="md:col-span-2 lg:col-span-3 bg-white rounded-xl shadow-lg p-6 md:p-8 flex flex-col md:flex-row items-center gap-6 border-l-8 border-[#ffa600]">
                <div class="text-center md:text-left">
                    <p class="text-lg text-gray-600 mb-2">A key finding from the needs assessment reveals a strong demand for enhanced security skills:</p>
                    <h2 class="text-2xl font-bold text-[#003f5c]">Top Priority: Cybersecurity</h2>
                    <p class="text-gray-500 mt-2">This highlights the critical need to protect sensitive data and institutional integrity in an increasingly digital operational environment.</p>
                </div>
                <div class="flex-shrink-0 text-center">
                    <p class="text-7xl lg:text-8xl font-black text-[#ff7c43]">57%</p>
                    <p class="text-sm text-gray-500 font-semibold">(12 of 21 Officers)</p>
                    <p class="text-base font-bold text-[#003f5c]">Requested Advanced Training</p>
                </div>
            </div>

            <div class="lg:col-span-2 bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h2 class="text-2xl font-bold text-[#003f5c] mb-1">Current Digital Proficiency</h2>
                <p class="text-gray-600 mb-6">Officers assessed their own proficiency on a scale of 1 (Novice) to 5 (Expert). The results show a solid foundation but clear opportunities for growth in advanced creative and analytical tools.</p>
                <div class="chart-container max-w-3xl">
                    <canvas id="proficiencyChart"></canvas>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h2 class="text-2xl font-bold text-[#003f5c] mb-1">Top Training Demands</h2>
                <p class="text-gray-600 mb-6">When asked what skills they wanted to learn most, personnel showed overwhelming interest in data analysis, cybersecurity, and content creation.</p>
                <div class="chart-container max-w-lg">
                    <canvas id="interestChart"></canvas>
                </div>
            </div>
            
            <div class="md:col-span-2 lg:col-span-3 bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h2 class="text-2xl font-bold text-center text-[#003f5c] mb-1">The Digital Workflow Divide</h2>
                <p class="text-gray-600 text-center mb-6 max-w-3xl mx-auto">A significant gap exists between the frequent use of tools for routine administrative tasks and the infrequent use of tools for high-impact creative content. The training aims to bridge this divide.</p>
                <div class="w-full max-w-4xl mx-auto">
                    <div class="flex items-center justify-between text-xs md:text-sm font-bold text-white mb-2">
                        <span class="text-left text-[#2f4b7c]">Routine Tasks (Daily/Weekly)</span>
                        <span class="text-right text-[#d45087]">Creative Tasks (Rarely/Never)</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-8 flex overflow-hidden">
                        <div class="bg-[#2f4b7c] h-full flex items-center justify-center text-white font-bold text-lg" style="width: 70%;">70%</div>
                        <div class="bg-[#d45087] h-full flex items-center justify-center text-white font-bold text-lg" style="width: 30%;">30%</div>
                    </div>
                     <p class="text-xs text-center text-gray-500 mt-3">This visualization represents the disparity where a majority of digital activity is administrative, while creative content creation, a key area for growth and public engagement, is less frequent.</p>
                </div>
            </div>

            <div class="md:col-span-2 lg:col-span-3 bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h2 class="text-2xl font-bold text-center text-[#003f5c] mb-6">The 2.5-Day Transformation Plan</h2>
                <p class="text-gray-600 text-center mb-8 max-w-3xl mx-auto">The proposed schedule is a logical progression, building foundational skills first and moving towards advanced creative and strategic applications.</p>
                <div class="flex flex-col md:flex-row justify-between gap-4 text-center">
                    
                    <div class="flex-1">
                        <h3 class="text-xl font-bold text-[#665191] mb-4 border-b-4 border-[#665191] pb-2">Day 1: Foundations</h3>
                        <div class="space-y-3 text-left">
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Google Docs:</strong> Collaborative Document Management</div>
                            <div class="text-2xl text-center flow-arrow">&darr;</div>
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Google Sheets (Part 1):</strong> Advanced Data Analysis</div>
                        </div>
                    </div>
                    
                    <div class="hidden md:block text-4xl text-gray-300 mx-4 mt-16">&rarr;</div>

                    <div class="flex-1">
                        <h3 class="text-xl font-bold text-[#d45087] mb-4 border-b-4 border-[#d45087] pb-2">Day 2: Application</h3>
                        <div class="space-y-3 text-left">
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Google Sheets (Part 2):</strong> Reporting & Visualization</div>
                            <div class="text-2xl text-center flow-arrow">&darr;</div>
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Google Slides:</strong> Professional Presentations</div>
                             <div class="text-2xl text-center flow-arrow">&darr;</div>
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Canva:</strong> Strategic Visual Content Creation</div>
                        </div>
                    </div>

                    <div class="hidden md:block text-4xl text-gray-300 mx-4 mt-16">&rarr;</div>

                    <div class="flex-1">
                        <h3 class="text-xl font-bold text-[#ff7c43] mb-4 border-b-4 border-[#ff7c43] pb-2">Day 3: Strategic Impact</h3>
                        <div class="space-y-3 text-left">
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Video Editing:</strong> Strategic Storytelling</div>
                            <div class="text-2xl text-center flow-arrow">&darr;</div>
                            <div class="bg-gray-100 p-3 rounded-lg"><strong>Cybersecurity:</strong> Enhanced Digital Vigilance</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="md:col-span-2 lg:col-span-3 bg-white rounded-xl shadow-lg p-6 md:p-8">
                <h2 class="text-2xl font-bold text-center text-[#003f5c] mb-6">Anticipated Outcomes</h2>
                <p class="text-gray-600 text-center mb-8 max-w-3xl mx-auto">This training will directly address identified challenges and empower officers with skills that translate into tangible operational improvements.</p>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 text-center">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <div class="text-4xl mb-3">🚀</div>
                        <h3 class="font-bold text-lg text-[#003f5c]">Increased Efficiency</h3>
                        <p class="text-sm text-gray-600">Streamline reporting, data management, and collaborative tasks.</p>
                    </div>
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <div class="text-4xl mb-3">💬</div>
                        <h3 class="font-bold text-lg text-[#665191]">Enhanced Communication</h3>
                        <p class="text-sm text-gray-600">Create professional presentations and clear internal announcements.</p>
                    </div>
                    <div class="bg-pink-50 p-6 rounded-lg">
                        <div class="text-4xl mb-3">🌟</div>
                        <h3 class="font-bold text-lg text-[#d45087]">Improved Public Relations</h3>
                        <p class="text-sm text-gray-600">Develop engaging social media content and public advisories.</p>
                    </div>
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <div class="text-4xl mb-3">🛡️</div>
                        <h3 class="font-bold text-lg text-[#ff7c43]">Strengthened Security</h3>
                        <p class="text-sm text-gray-600">Proactively defend against cyber threats and protect sensitive data.</p>
                    </div>
                </div>
            </div>

        </main>
        
        <footer class="text-center mt-12 text-sm text-gray-500">
            <p>Infographic based on the Digital Literacy Needs Assessment for BJMP Bislig Operations, August 2025.</p>
            <p>This report was generated to support the DICT Region XIII training initiative.</p>
        </footer>

    </div>

    <script>
        const brilliantBluesPalette = {
            deepBlue: '#003f5c',
            indigo: '#2f4b7c',
            purple: '#665191',
            magenta: '#a05195',
            pink: '#d45087',
            coral: '#f95d6a',
            orange: '#ff7c43',
            gold: '#ffa600'
        };

        const tooltipCallback = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                                return label.join(' ');
                            }
                            return label;
                        }
                    }
                },
                legend: {
                    labels: {
                        font: {
                            family: "'Inter', sans-serif"
                        }
                    }
                }
            },
            scales: {
                y: {
                    ticks: {
                        font: { family: "'Inter', sans-serif" }
                    },
                    grid: {
                        drawOnChartArea: false
                    }
                },
                x: {
                    ticks: {
                        font: { family: "'Inter', sans-serif" }
                    },
                     grid: {
                        display: false
                    }
                }
            }
        };

        function wrapLabel(str, maxWidth) {
            if (str.length <= maxWidth) {
                return str;
            }
            const words = str.split(' ');
            let lines = [];
            let currentLine = '';
            for (const word of words) {
                if ((currentLine + word).length > maxWidth) {
                    lines.push(currentLine.trim());
                    currentLine = '';
                }
                currentLine += word + ' ';
            }
            lines.push(currentLine.trim());
            return lines;
        }

        function renderProficiencyChart() {
            const ctx = document.getElementById('proficiencyChart').getContext('2d');
            const rawLabels = ['Cybersecurity & Online Safety', 'Google Docs (Advanced)', 'Google Sheets (Advanced)', 'Google Slides (Advanced)', 'Canva Graphic Design', 'Video Editing Basics'];
            const labels = rawLabels.map(label => wrapLabel(label, 20));
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Average Self-Assessed Proficiency (out of 5)',
                        data: [3.0, 2.9, 2.9, 2.7, 2.3, 2.3],
                        backgroundColor: [
                            brilliantBluesPalette.deepBlue,
                            brilliantBluesPalette.indigo,
                            brilliantBluesPalette.purple,
                            brilliantBluesPalette.magenta,
                            brilliantBluesPalette.pink,
                            brilliantBluesPalette.coral
                        ],
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        borderRadius: 4,
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    ...tooltipCallback,
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 5,
                            ticks: {
                                font: { family: "'Inter', sans-serif" }
                            },
                             grid: {
                                display: true,
                                drawOnChartArea: true,
                                color: '#e5e7eb'
                            }
                        },
                        y: {
                            ticks: {
                                font: { family: "'Inter', sans-serif" }
                            },
                            grid: {
                                display: false
                            }
                        }
                    },
                    plugins: {
                        ...tooltipCallback.plugins,
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function renderInterestChart() {
            const ctx = document.getElementById('interestChart').getContext('2d');
            const rawLabels = ['Cybersecurity', 'Google Sheets', 'Google Docs', 'Video Editing', 'Canva Design'];
            const labels = rawLabels.map(label => wrapLabel(label, 16));
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Number of Requests',
                        data: [12, 12, 10, 9, 8],
                        backgroundColor: [
                            brilliantBluesPalette.gold,
                            brilliantBluesPalette.orange,
                            brilliantBluesPalette.coral,
                            brilliantBluesPalette.pink,
                            brilliantBluesPalette.magenta
                        ],
                        borderColor: '#ffffff',
                        borderWidth: 4,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '60%',
                    plugins: {
                         ...tooltipCallback.plugins,
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                font: {
                                    size: 12,
                                    family: "'Inter', sans-serif"
                                }
                            }
                        }
                    }
                }
            });
        }

        window.onload = function() {
            renderProficiencyChart();
            renderInterestChart();
        };
    </script>
</body>
</html>
